@startuml Chatbot Basic Flow
User -> Application : Sends Message
Application -> Chatbot API : Sends list of messages to Chatbot API
note left
1. Adds System Prompt
2. Adds User Message
end note
Chatbot API > Application : Returns Response
note right
1. response object includes other attributesResponse
end note
Application -> Application : Adds response to list of messages with role "assistant"
Application -> User : Returns Response
@enduml
