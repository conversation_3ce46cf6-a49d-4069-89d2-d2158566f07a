# A script that:
# 1. Uses OpenAI to generate different user personas
# 2. Saves personas into MongoDB

import pymongo
from openai import OpenAI
from domain.user_persona import UserPersonas
import json
import uuid



openai_api_key = "********************************************************************************************************************************************************************"

open_ai_client = OpenAI(api_key=openai_api_key)

client = pymongo.MongoClient("mongodb://localhost:27017/")

db = client["surveys"]

survey_collection = db["user_personas"]

# Generate Survey with OpenAI API call

user_prompt = """
    Please help me create 5 different user personas ranging in different ages and professions
    """

for user_id in range(5):
    response = open_ai_client.beta.chat.completions.parse(
        model="gpt-4o-mini",
        messages=[
            {"role": "user", "content": user_prompt}
        ],
        response_format=UserPersonas
    )

    response_content  = response.choices[0].message.content
    response_dict = json.loads(response_content)
    user_personas = UserPersonas(**response_dict)

    for user_persona in user_personas.user_personas:
        user_persona.user_persona_id = str(uuid.uuid4())
        survey_collection.insert_one(user_persona.model_dump())

client.close()
