from bson import ObjectId
import pymongo
from openai import OpenAI
import json
from domain.user_persona import User<PERSON><PERSON><PERSON>, QuestionAnswerPairs, KnowledgeBase
from domain.survey import Survey
from rag import AnswerRag
from repository.user_persona_repository import get_user_id, update_user_id

rag = AnswerRag()

openai_api_key = "********************************************************************************************************************************************************************"
open_ai_client = OpenAI(api_key=openai_api_key)


client = pymongo.MongoClient("mongodb://localhost:27017/")
database = client["surveys"]

# take 1 survey - music_edu_survey_2023
survey = database["surveys"].find_one({"survey_id": "music_edu_survey_2023"})
survey = Survey(**survey)

# take 1 user persona - a30c55eb-e7da-4f9e-afe8-3ce4ba0e9e46 - Samantha Roberts - objectId - 6801ff202711cf54d2eb29a8
user_persona: UserPersona = get_user_id({"user_persona_id": "a30c55eb-e7da-4f9e-afe8-3ce4ba0e9e46"})

# generate answers for user persona based on survey questions

system_prompt = """
    You are {user_persona.full_name}, a {user_persona.age} year old {user_persona.gender} with interests in {user_persona.interests} and personality traits {user_persona.personality_traits}.
    {user_persona.description}
    You will be asked a series of questions. Please answer the following questions succinctly.
    """

for question in survey.questions:
    # query RAG system first
    rag_response = rag.get_response(question.question, {"user_persona_id": user_persona.user_persona_id})
    
    if (rag_response.is_certain):
        user_persona.knowledge_base.questions_and_answers.append(rag_response.model_dump())
        continue
    
    user_prompt = f"""
        Question: 
        {question.question}
        Type:
        {question.type}
        Options:
        {question.options}
    """

    # generate answer for question

    response = open_ai_client.beta.chat.completions.parse(
        model="gpt-4o-mini",
        messages=[
            {"role": "system", "content": system_prompt.format(user_persona=user_persona)},
            {"role": "user", "content": user_prompt}
        ],
        response_format=QuestionAnswerPairs
    )

    response_content  = response.choices[0].message.content
    response_dict = json.loads(response_content)
    question_answer_pair = QuestionAnswerPairs(**response_dict)

    # save question answer pair into knowledge base
    if user_persona.knowledge_base is None:
        user_persona.knowledge_base = KnowledgeBase()

    # naive knowledge base - directly append question and answers into knowledge base
    user_persona.knowledge_base.questions_and_answers.append(question_answer_pair)

    # save question answer pair into RAG
    rag.add_document(question_answer_pair.question, {"user_persona_id": user_persona.user_persona_id, "answer": question_answer_pair.answer})

    # save user persona into MongoDB
    update_user_id(user_persona)

    print("Question: ")
    print(question.question)
    print("Answer: ")
    print(question_answer_pair.answer)
    print("\n")

client.close()
