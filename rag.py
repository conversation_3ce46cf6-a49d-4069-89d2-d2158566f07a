from sentence_transformers import SentenceTransformer
from sklearn.metrics.pairwise import cosine_similarity
import chromadb
from openai import OpenAI
from domain.user_persona import QuestionAnswerPairs
import json


openai_api_key = "********************************************************************************************************************************************************************"
open_ai_client = OpenAI(api_key=openai_api_key)

class AnswerRag:
    def __init__(self, collection_name: str = "answer_rag", model_name: str = "all-distilroberta-v1", path="/.chroma/answer_rag"):
        self.collection_name = collection_name
        self.model_name = model_name
        self.path = path
        self.client = chromadb.PersistentClient(path=self.path)
        self.collection = self.client.get_or_create_collection(name=self.collection_name)
        self.model = SentenceTransformer(self.model_name)
    
    def format(self, results: dict):
        results = {
            "documents": results["documents"][0],
            "metadatas": results["metadatas"][0],
            "distances": results["distances"][0],
            "ids": results["ids"][0]
        }
        # currently each document/metadata/distances/ids is a list of 10 items - to zip them up 

        formatted_results = [{"document": document, "metadata": metadata, "distance": distance, "id": id} for document, metadata, distance, id in list(zip(results["documents"], results["metadatas"], results["distances"], results["ids"]))]
        return formatted_results

    def query(self, query, metadata_filter=None):
        # defaults to top 10 results
        query_embedding = self.model.encode([query])[0]
        if not metadata_filter:
            results = self.collection.query(
                query_embedding, 
                include=["metadatas", "documents","distances"])
        else:
            results = self.collection.query(
                query_embedding, 
                where=metadata_filter,
                include=["metadatas", "documents","distances"])
        return self.format(results)

    def add_document(self, document, metadata=None):
        document_embedding = self.model.encode([document])[0]
        self.collection.add(
            documents=[document],
            metadatas=[metadata],
            ids=[str(hash(document))],
            embeddings=[document_embedding])
    
    def get_response(self, query, metadata_filter=None) -> QuestionAnswerPairs:
        results = self.query(query, metadata_filter)

        # answers are in the metadata - if no "answer" key, return empty string
        # extract each question and answer pair from results - question is in results["documents"] while answer is in results["metadatas"]["answer"]
        question_answer_pairs = [(result['document'], result["metadata"]["answer"]) if "answer" in result["metadata"] else "" for result in results]

        system_prompt = """
            You are a personal assistant and are helping a user answer some questions about him/herself.
            You recall some information about him/her:
            {question_answer_pairs}
            Please answer this question appropriately given the information - otherwise, you should set is_certain to False
        """

        response = open_ai_client.beta.chat.completions.parse(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": system_prompt.format(question_answer_pairs=question_answer_pairs)},
                {"role": "user", "content": query}
            ],
            response_format=QuestionAnswerPairs
        )

        response_content  = response.choices[0].message.content
        response_dict = json.loads(response_content)
        question_answer_pair = QuestionAnswerPairs(**response_dict)

        return question_answer_pair

