from domain.user_persona import UserPersona
import pymongo
from rag import AnswerRag

client = pymongo.MongoClient("mongodb://localhost:27017/")
db = client["surveys"]
user_persona_collection = db["user_personas"]
rag = AnswerRag()

def get_user_id(metadata):
    user_persona = user_persona_collection.find_one({"user_persona_id": metadata["user_persona_id"]})
    return UserPersona(**user_persona)

def update_user_id(user_persona):
    user_persona_collection.update_one(
        {"user_persona_id": user_persona.user_persona_id},
        {"$set": user_persona.model_dump()}
    )

'''
Overloaded method - if query is None or empty, simply retrieve by user_id in metadata and return
Otherwise, apply RAG to query and return result
'''

def generate_result_from_persona(query, metadata):
    if query is None:    
        user_persona: UserPersona = get_user_id(metadata)
        return user_persona.model_dump_json()
    
    # apply RAG to query and return result
    result = rag.get_response(query, metadata)
    return result