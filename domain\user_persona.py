from bson import ObjectId
from pydantic import BaseModel, Field
from pydantic_mongo import AbstractRepository, PydanticObjectId
from typing import Optional

class QuestionAnswerPairs(BaseModel):
    question: str
    answer: str
    is_certain: bool

class Topic(BaseModel):
    topic_id: str
    topic_title: str
    description: str
    questions_and_answers: list[QuestionAnswerPairs]
    subtopics: list["Topic"] = []
    parent_topic_id: Optional[str] = None

class KnowledgeBase(BaseModel):
    knowledge_base: list[Topic] = []
    questions_and_answers: list[QuestionAnswerPairs] = []

class UserPersona(BaseModel):
    id: Optional[PydanticObjectId] = Field(None, alias="_id")
    user_persona_id: str
    full_name: str
    description: str
    age: int
    gender: str
    interests: list[str]
    personality_traits: list[str]
    knowledge_base: Optional[KnowledgeBase] = None

    class Config:
        populate_by_name = True

    

class UserPersonas(BaseModel):
    user_personas: list[UserPersona]

class UserRepository(AbstractRepository[UserPersona]):
    class Meta:
        collection_name = 'user_personas'
