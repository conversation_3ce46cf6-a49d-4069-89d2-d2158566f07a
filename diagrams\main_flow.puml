@startuml Overall Flow
Main -> Profile : ask question
note right
Persona Details:
- demographic information (age, gender, race, country)
- brief description
- hobbies
- interests
- personality
- character
end note
group Per Question
group First Line - RAG-based Response (Auto-answering)
Profile -> RAG: queries RAG with question
note left
metadata filter - userid
relevancy details?
any other metadata hacking can be done?
end note
RAG -> Profile: responds with results from vector stores (top 10 documents)
Profile -> Profile: filter resultant answers based on distance threshold
Profile -> LLMA: question with System Prompt
note left
Given these previous questions and answers,
{Questions,Answers}
please help answer this question
Else return object indicating "isCertain": False
end note
LLMA -> Profile: response - structured object
end group

alt not isCertain (ask actual user)
Profile -> LLMU: question with System Prompt
note left
You are {name}, with this personality traits...
Please answer this question...
end note
LLMU -> Profile: response
end alt
Profile -> RAG: persist generated response into RAG ? (boolean switch)
Profile -> UserHistory: persist into User History
end group
@enduml