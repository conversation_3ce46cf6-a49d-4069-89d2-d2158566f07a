from flask import Flask, request, jsonify
from rag import AnswerRag
from domain.user_persona import Question<PERSON><PERSON>wer<PERSON><PERSON><PERSON>, UserPersona
from repository.user_persona_repository import generate_result_from_persona
app = Flask(__name__)

rag = AnswerRag()

@app.route("/")
def home():
    return "Welcome to the Weave Flask app!"

@app.route("/get_result_from_persona", methods=["POST"])
def get_result_from_persona() -> UserPersona:
    data = request.get_json()
    query = data.get("query")
    metadata = data.get("metadata", {})
    return generate_result_from_persona(query, metadata)

@app.route("/get_persona", methods=["GET"])
def get_persona():
    user_persona_id = request.args['user_persona_id']
    return generate_result_from_persona({}, {'user_persona_id': user_persona_id})

@app.route("/queryRagForDocuments", methods = ["POST"])
def query_rag_for_documents():
    data = request.get_json()
    query = data.get("query")
    user_persona_id = data.get("user_persona_id")
    metadata = {
        "user_persona_id": user_persona_id
    }
    return rag.query(query, metadata)

@app.route("/search", methods=["POST"])
def search_rag():
    data = request.get_json()
    query = data.get("query")
    metadata = data.get("metadata", {})
    if not query:
        return jsonify({"error": "Query is required"}), 400
    # Call RAG search (adjust method as needed)
    result: QuestionAnswerPairs = rag.get_response(query, metadata)
    if not result.is_certain:
        result = dict(generate_result_from_persona(query, metadata))
        result["pre-answered"] = False
    # If result is a Pydantic model, convert to dict
    if hasattr(result, "model_dump"):
        result = result.model_dump()
        result["pre-answered"] = True

    # if result 
    return jsonify(result)

if __name__ == "__main__":
    app.run(debug=True)