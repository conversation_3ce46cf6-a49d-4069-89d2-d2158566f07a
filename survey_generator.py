# A script that:
# 1. Uses OpenAI to generate a survey
# 2. Saves survey into MongoDB

import pymongo
from openai import OpenAI
from domain.survey import Survey
import json



openai_api_key = "********************************************************************************************************************************************************************"

open_ai_client = OpenAI(api_key=openai_api_key)

client = pymongo.MongoClient("mongodb://localhost:27017/")

db = client["surveys"]

# A survey takes has an ID and is linked to various underlying questions which are stored in a separate collection in the same db

survey_collection = db["surveys"]
question_collection = db["questions"]

# Generate Survey with OpenAI API call

system_prompt = """
    You are an analyst in a consumer research company, helping a company in a particular domain to create surveys.
    """

user_prompt = """
    Please help me create a survey to understand my users better in the {industry} industry.
    """

industries = ["music education", "men's fashion", "female fashion", "furniture"]

for industry in industries:
    response = open_ai_client.beta.chat.completions.parse(
        model="gpt-4o-mini",
        messages=[
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt.format(industry=industry)}
        ],
        response_format=Survey
    )

    response_content  = response.choices[0].message.content
    response_dict = json.loads(response_content)
    survey = Survey(**response_dict)

    survey_collection.insert_one(survey.model_dump())

client.close()
